// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functionality
    initMobileMenu();
    initTestimonialSlider();
    initFreelancerSlider();
    initFAQ();
    initCounters();
    initCookieNotice();
    initScrollAnimations();
});

// Mobile Menu Toggle
function initMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (mobileToggle && navMenu) {
        mobileToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            mobileToggle.classList.toggle('active');
        });
    }
}

// Testimonial Slider
let currentTestimonial = 0;
const testimonials = document.querySelectorAll('.testimonial-item');
const testimonialDots = document.querySelectorAll('.testimonial-dots .dot');

function initTestimonialSlider() {
    if (testimonials.length > 0) {
        showTestimonial(0);
        
        // Auto-slide every 5 seconds
        setInterval(() => {
            currentTestimonial = (currentTestimonial + 1) % testimonials.length;
            showTestimonial(currentTestimonial);
        }, 5000);
    }
}

function showTestimonial(index) {
    // Hide all testimonials
    testimonials.forEach(testimonial => {
        testimonial.classList.remove('active');
    });
    
    // Remove active class from all dots
    testimonialDots.forEach(dot => {
        dot.classList.remove('active');
    });
    
    // Show current testimonial
    if (testimonials[index]) {
        testimonials[index].classList.add('active');
    }
    
    // Activate current dot
    if (testimonialDots[index]) {
        testimonialDots[index].classList.add('active');
    }
}

function currentSlide(index) {
    currentTestimonial = index - 1;
    showTestimonial(currentTestimonial);
}

// Freelancer Slider
function initFreelancerSlider() {
    const prevBtn = document.querySelector('.freelancers-slider .prev-btn');
    const nextBtn = document.querySelector('.freelancers-slider .next-btn');
    const freelancersGrid = document.querySelector('.freelancers-grid');
    
    if (prevBtn && nextBtn && freelancersGrid) {
        let currentIndex = 0;
        const cards = freelancersGrid.querySelectorAll('.freelancer-card');
        const cardsPerView = getCardsPerView();
        const maxIndex = Math.max(0, cards.length - cardsPerView);
        
        prevBtn.addEventListener('click', () => {
            if (currentIndex > 0) {
                currentIndex--;
                updateSliderPosition();
            }
        });
        
        nextBtn.addEventListener('click', () => {
            if (currentIndex < maxIndex) {
                currentIndex++;
                updateSliderPosition();
            }
        });
        
        function updateSliderPosition() {
            const cardWidth = cards[0].offsetWidth + 32; // 32px for gap
            freelancersGrid.style.transform = `translateX(-${currentIndex * cardWidth}px)`;
        }
        
        function getCardsPerView() {
            const containerWidth = freelancersGrid.parentElement.offsetWidth;
            const cardWidth = 332; // 300px + 32px gap
            return Math.floor(containerWidth / cardWidth);
        }
        
        // Update on window resize
        window.addEventListener('resize', () => {
            const newCardsPerView = getCardsPerView();
            const newMaxIndex = Math.max(0, cards.length - newCardsPerView);
            if (currentIndex > newMaxIndex) {
                currentIndex = newMaxIndex;
                updateSliderPosition();
            }
        });
    }
}

// FAQ Accordion
function initFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        
        question.addEventListener('click', () => {
            const isActive = item.classList.contains('active');
            
            // Close all FAQ items
            faqItems.forEach(faqItem => {
                faqItem.classList.remove('active');
            });
            
            // Open clicked item if it wasn't active
            if (!isActive) {
                item.classList.add('active');
            }
        });
    });
    
    // Show more button
    const showMoreBtn = document.querySelector('.show-more-btn');
    if (showMoreBtn) {
        showMoreBtn.addEventListener('click', () => {
            // Add more FAQ items or show hidden ones
            alert('More FAQ items would be loaded here');
        });
    }
}

// Counter Animation
function initCounters() {
    const counters = document.querySelectorAll('.counter');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.getAttribute('data-target') || '0');
        const duration = 2000; // 2 seconds
        const increment = target / (duration / 16); // 60fps
        let current = 0;
        
        const updateCounter = () => {
            current += increment;
            if (current < target) {
                counter.textContent = Math.floor(current);
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target;
            }
        };
        
        updateCounter();
    };
    
    // Intersection Observer for counter animation
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                if (!counter.classList.contains('animated')) {
                    counter.classList.add('animated');
                    animateCounter(counter);
                }
            }
        });
    });
    
    counters.forEach(counter => {
        counter.setAttribute('data-target', counter.textContent);
        counter.textContent = '0';
        counterObserver.observe(counter);
    });
}

// Cookie Notice
function initCookieNotice() {
    const cookieNotice = document.getElementById('cookie-notice');
    
    // Show cookie notice if not accepted
    if (!localStorage.getItem('cookiesAccepted')) {
        setTimeout(() => {
            cookieNotice.classList.add('show');
        }, 2000);
    }
}

function acceptCookies() {
    localStorage.setItem('cookiesAccepted', 'true');
    document.getElementById('cookie-notice').classList.remove('show');
}

function rejectCookies() {
    localStorage.setItem('cookiesAccepted', 'false');
    document.getElementById('cookie-notice').classList.remove('show');
}

// Scroll Animations
function initScrollAnimations() {
    const animatedElements = document.querySelectorAll('.category-card, .step-item, .feature-item, .freelancer-card, .blog-card');
    
    const scrollObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    animatedElements.forEach(element => {
        scrollObserver.observe(element);
    });
}

// Newsletter Subscription
function subscribeNewsletter() {
    const emailInput = document.querySelector('.newsletter-input');
    const email = emailInput.value.trim();
    
    if (email && isValidEmail(email)) {
        // Simulate newsletter subscription
        alert('Thank you for subscribing to our newsletter!');
        emailInput.value = '';
    } else {
        alert('Please enter a valid email address.');
    }
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Search Functionality
function performSearch() {
    const searchInput = document.querySelector('.search-input');
    const categorySelect = document.querySelector('.filter-select');
    const typeSelect = document.querySelectorAll('.filter-select')[1];
    
    const searchTerm = searchInput.value.trim();
    const category = categorySelect.value;
    const type = typeSelect.value;
    
    if (searchTerm) {
        // Simulate search functionality
        console.log('Searching for:', {
            term: searchTerm,
            category: category,
            type: type
        });
        
        alert(`Searching for "${searchTerm}" in ${category} (${type})`);
    } else {
        alert('Please enter a search term.');
    }
}

// Add event listeners for interactive elements
document.addEventListener('DOMContentLoaded', function() {
    // Newsletter form
    const newsletterBtn = document.querySelector('.newsletter-btn');
    if (newsletterBtn) {
        newsletterBtn.addEventListener('click', subscribeNewsletter);
    }
    
    // Search button
    const searchBtn = document.querySelector('.search-btn');
    if (searchBtn) {
        searchBtn.addEventListener('click', performSearch);
    }
    
    // Search on Enter key
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }
    
    // Newsletter input on Enter key
    const newsletterInput = document.querySelector('.newsletter-input');
    if (newsletterInput) {
        newsletterInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                subscribeNewsletter();
            }
        });
    }
});

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Back to top functionality
window.addEventListener('scroll', function() {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    
    // Show/hide back to top button (if you add one)
    const backToTopBtn = document.querySelector('.back-to-top');
    if (backToTopBtn) {
        if (scrollTop > 300) {
            backToTopBtn.classList.add('show');
        } else {
            backToTopBtn.classList.remove('show');
        }
    }
});
