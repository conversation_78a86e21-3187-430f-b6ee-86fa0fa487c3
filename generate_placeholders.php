<?php
// Generate placeholder images for the Olance website

function createPlaceholderImage($width, $height, $text, $filename, $bgColor = '#4F46E5', $textColor = '#FFFFFF') {
    // Create image
    $image = imagecreate($width, $height);
    
    // Allocate colors
    $bg = imagecolorallocate($image, hexdec(substr($bgColor, 1, 2)), hexdec(substr($bgColor, 3, 2)), hexdec(substr($bgColor, 5, 2)));
    $textColorRGB = imagecolorallocate($image, hexdec(substr($textColor, 1, 2)), hexdec(substr($textColor, 3, 2)), hexdec(substr($textColor, 5, 2)));
    
    // Fill background
    imagefill($image, 0, 0, $bg);
    
    // Add text
    $fontSize = min($width, $height) / 10;
    $textBox = imagettfbbox($fontSize, 0, __DIR__ . '/arial.ttf', $text);
    
    // If TTF font not available, use built-in font
    if (!file_exists(__DIR__ . '/arial.ttf')) {
        $fontSize = 5;
        $textWidth = imagefontwidth($fontSize) * strlen($text);
        $textHeight = imagefontheight($fontSize);
        $x = ($width - $textWidth) / 2;
        $y = ($height - $textHeight) / 2;
        imagestring($image, $fontSize, $x, $y, $text, $textColorRGB);
    } else {
        $textWidth = $textBox[4] - $textBox[0];
        $textHeight = $textBox[1] - $textBox[5];
        $x = ($width - $textWidth) / 2;
        $y = ($height - $textHeight) / 2 + $textHeight;
        imagettftext($image, $fontSize, 0, $x, $y, $textColorRGB, __DIR__ . '/arial.ttf', $text);
    }
    
    // Save image
    imagepng($image, $filename);
    imagedestroy($image);
    
    echo "Created: $filename\n";
}

// Create main logo
createPlaceholderImage(200, 60, 'OLANCE', 'assets/images/logo.png', '#4F46E5', '#FFFFFF');

// Create hero banner
createPlaceholderImage(600, 400, 'Hero Banner', 'assets/images/hero-banner.png', '#667eea', '#FFFFFF');

// Create category icons
$categories = [
    'web-dev' => 'Web Dev',
    'writing' => 'Writing',
    'marketing' => 'Marketing',
    'design' => 'Design',
    'it' => 'IT',
    'business' => 'Business',
    'mobile' => 'Mobile'
];

foreach ($categories as $key => $name) {
    createPlaceholderImage(80, 80, $name, "assets/images/categories/$key.png", '#E5E7EB', '#374151');
}

// Create step icons
$steps = [
    'post-job' => 'Post',
    'hire' => 'Hire',
    'work-done' => 'Work',
    'payment' => 'Pay'
];

foreach ($steps as $key => $name) {
    createPlaceholderImage(60, 60, $name, "assets/images/icons/$key.png", '#4F46E5', '#FFFFFF');
}

// Create feature icons
$features = [
    'quality' => 'Quality',
    'no-cost' => 'No Cost',
    'secure' => 'Secure',
    'post-hire' => 'Post',
    'bid' => 'Bid',
    'top-rated' => 'Top'
];

foreach ($features as $key => $name) {
    createPlaceholderImage(60, 60, $name, "assets/images/icons/$key.png", '#4F46E5', '#FFFFFF');
}

// Create signup images
createPlaceholderImage(150, 150, 'Freelancer', 'assets/images/freelancer-signup.png', '#10B981', '#FFFFFF');
createPlaceholderImage(150, 150, 'Buyer', 'assets/images/buyer-signup.png', '#F59E0B', '#FFFFFF');

// Create find task images
createPlaceholderImage(400, 300, 'Find Task 1', 'assets/images/find-task-1.png', '#8B5CF6', '#FFFFFF');
createPlaceholderImage(300, 200, 'Find Task 2', 'assets/images/find-task-2.png', '#06B6D4', '#FFFFFF');

// Create different section image
createPlaceholderImage(500, 400, 'Different', 'assets/images/different-section.png', '#EC4899', '#FFFFFF');

// Create completion work image
createPlaceholderImage(500, 400, 'Completion', 'assets/images/completion-work.png', '#84CC16', '#FFFFFF');

// Create testimonial avatars
$testimonials = [
    'john' => 'John',
    'sarah' => 'Sarah',
    'richitya' => 'Richitya',
    'quinn' => 'Quinn',
    'emily' => 'Emily'
];

foreach ($testimonials as $key => $name) {
    createPlaceholderImage(100, 100, $name, "assets/images/testimonials/$key.png", '#6366F1', '#FFFFFF');
}

// Create freelancer avatars
$freelancers = [
    'freelancer1' => 'FL1',
    'quinn' => 'Quinn',
    'logias' => 'Logias'
];

foreach ($freelancers as $key => $name) {
    createPlaceholderImage(100, 100, $name, "assets/images/freelancers/$key.jpg", '#8B5CF6', '#FFFFFF');
}

// Create blog images
for ($i = 1; $i <= 6; $i++) {
    createPlaceholderImage(300, 200, "Blog $i", "assets/images/blog/blog$i.png", '#F59E0B', '#FFFFFF');
}

// Create newsletter images
createPlaceholderImage(200, 200, 'Newsletter', 'assets/images/newsletter-bg.png', '#4F46E5', '#FFFFFF');
createPlaceholderImage(100, 100, 'Shape', 'assets/images/newsletter-shape.png', '#7C3AED', '#FFFFFF');
createPlaceholderImage(150, 200, 'Person', 'assets/images/newsletter-person.png', '#10B981', '#FFFFFF');

// Create FAQ image
createPlaceholderImage(400, 300, 'FAQ', 'assets/images/faq-image.png', '#EF4444', '#FFFFFF');

// Create flag images
$flags = [
    'en' => 'EN',
    'fr' => 'FR',
    'es' => 'ES'
];

foreach ($flags as $key => $name) {
    createPlaceholderImage(30, 20, $name, "assets/images/flags/$key.png", '#1F2937', '#FFFFFF');
}

echo "\nAll placeholder images created successfully!\n";
?>
