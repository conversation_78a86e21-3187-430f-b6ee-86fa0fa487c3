/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    color: #666;
}

.highlight {
    color: #06B6D4;
    position: relative;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    cursor: pointer;
    font-size: 14px;
}

.btn-primary {
    background-color: #06B6D4;
    color: white;
    border-color: #06B6D4;
}

.btn-primary:hover {
    background-color: #0891B2;
    border-color: #0891B2;
}

.btn-outline {
    background-color: transparent;
    color: #06B6D4;
    border-color: #06B6D4;
}

.btn-outline:hover {
    background-color: #06B6D4;
    color: white;
}

/* Header */
.header {
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo .logo {
    height: 40px;
    width: auto;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: #06B6D4;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.language-dropdown {
    position: relative;
}

.lang-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.3s ease;
}

.lang-btn:hover {
    background-color: #f3f4f6;
}

.flag-icon {
    width: 20px;
    height: 15px;
    object-fit: cover;
}

.lang-dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    min-width: 150px;
    display: none;
    z-index: 1001;
}

.language-dropdown:hover .lang-dropdown-content {
    display: block;
}

.lang-dropdown-content a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 8px 12px;
    text-decoration: none;
    color: #333;
    transition: background-color 0.3s ease;
}

.lang-dropdown-content a:hover {
    background-color: #f3f4f6;
}

.auth-buttons {
    display: flex;
    gap: 0.5rem;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background-color: #333;
    transition: all 0.3s ease;
}

/* Main Content */
.main-content {
    margin-top: 80px;
}

/* Hero Section */
.hero-section {
    background: #f8fafc;
    padding: 80px 0;
    color: #1e293b;
    position: relative;
    overflow: hidden;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.1;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #64748b;
}

.search-form {
    margin-bottom: 3rem;
}

.search-container {
    background: white;
    border-radius: 12px;
    padding: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.search-input {
    flex: 1;
    border: none;
    padding: 12px 16px;
    font-size: 16px;
    outline: none;
    color: #333;
}

.search-filters {
    display: flex;
    gap: 8px;
}

.filter-select {
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    background-color: #f8f9fa;
    color: #333;
    cursor: pointer;
    outline: none;
}

.search-btn {
    background-color: #06B6D4;
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-btn:hover {
    background-color: #0891B2;
}

.trusted-by {
    text-align: center;
}

.trusted-by span {
    display: block;
    margin-bottom: 1rem;
    color: #64748b;
}

.client-logos {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.hero-image {
    position: relative;
}

.banner-img {
    width: 100%;
    height: auto;
    border-radius: 12px;
}

.hero-stats {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat-item {
    background: rgba(255,255,255,0.95);
    padding: 12px 16px;
    border-radius: 8px;
    color: #1e293b;
    font-weight: 500;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(6, 182, 212, 0.2);
}

.great-job {
    background: rgba(6, 182, 212, 0.9);
    color: white;
}

.rating-stars {
    margin-top: 4px;
}

.rating-stars i {
    color: #fbbf24;
    font-size: 12px;
}

/* Categories Section */
.categories-section {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.category-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    border: 1px solid #e5e7eb;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.category-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
    object-fit: contain;
}

.category-card h5 {
    color: #333;
    margin-bottom: 0.5rem;
}

.category-card p {
    color: #666;
    font-size: 14px;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

/* How It Works Section */
.how-it-works {
    padding: 80px 0;
    background-color: white;
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
}

.step-item {
    text-align: center;
    position: relative;
}

.step-item:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 30px;
    right: -1.5rem;
    width: 3rem;
    height: 2px;
    background: linear-gradient(90deg, #06B6D4, transparent);
}

.step-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #06B6D4, #0891B2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.step-icon img {
    width: 40px;
    height: 40px;
    filter: brightness(0) invert(1);
}

.step-content h5 {
    color: #333;
    margin-bottom: 1rem;
}

.step-content p {
    color: #666;
    line-height: 1.6;
}

/* Sign Up Section */
.signup-section {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.signup-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
}

.signup-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    padding: 2rem;
    gap: 2rem;
}

.signup-content h3 {
    color: #333;
    margin-bottom: 1rem;
}

.signup-content p {
    color: #666;
    margin-bottom: 2rem;
}

.signup-image img {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 12px;
}

/* Why Choose Us Section */
.why-choose-us {
    padding: 80px 0;
    background-color: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-item {
    text-align: center;
    padding: 2rem;
    border-radius: 12px;
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #06B6D4, #0891B2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-icon img {
    width: 40px;
    height: 40px;
    filter: brightness(0) invert(1);
}

.feature-item h5 {
    color: #333;
    margin-bottom: 1rem;
}

.feature-item p {
    color: #666;
    line-height: 1.6;
}

/* Find Task Section */
.find-task-section {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.find-task-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.find-task-text h5 {
    color: #06B6D4;
    font-size: 1rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.find-task-text h2 {
    color: #333;
    margin-bottom: 1.5rem;
}

.find-task-text p {
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.task-benefits {
    list-style: none;
    margin-bottom: 2rem;
}

.task-benefits li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.task-benefits i {
    color: #10B981;
    font-size: 14px;
}

.find-task-images {
    position: relative;
}

.task-img-1 {
    width: 100%;
    border-radius: 12px;
}

.task-img-2 {
    position: absolute;
    bottom: -20px;
    right: -20px;
    width: 60%;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Different Section */
.different-section {
    padding: 80px 0;
    background-color: white;
}

.different-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.different-text h2 {
    color: #333;
    margin-bottom: 1.5rem;
}

.different-text > p {
    color: #666;
    margin-bottom: 3rem;
    line-height: 1.6;
}

.different-features {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.different-feature {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.feature-number {
    background: linear-gradient(135deg, #06B6D4, #0891B2);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
}

.different-feature h5 {
    color: #333;
    margin-bottom: 0.5rem;
}

.different-feature p {
    color: #666;
    line-height: 1.6;
}

.different-image img {
    width: 100%;
    border-radius: 12px;
}

/* Completion Work Section */
.completion-work {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.completion-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.completion-text h5 {
    color: #06B6D4;
    font-size: 1rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.completion-text h2 {
    color: #333;
    margin-bottom: 2rem;
}

.completion-benefits {
    list-style: none;
}

.completion-benefits li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.completion-benefits i {
    color: #10B981;
    font-size: 14px;
}

.completion-image img {
    width: 100%;
    border-radius: 12px;
}

/* Testimonials Section */
.testimonials-section {
    padding: 80px 0;
    background-color: white;
}

.testimonials-slider {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.testimonial-item {
    display: none;
    text-align: center;
    padding: 2rem;
}

.testimonial-item.active {
    display: block;
}

.testimonial-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    object-fit: cover;
}

.testimonial-content p {
    font-size: 1.1rem;
    color: #333;
    font-style: italic;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.testimonial-author h6 {
    color: #333;
    margin-bottom: 0.25rem;
}

.testimonial-author span {
    color: #666;
    font-size: 14px;
}

.testimonial-dots {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background-color: #d1d5db;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.dot.active,
.dot:hover {
    background-color: #06B6D4;
}

/* Freelancers Section */
.freelancers-section {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.freelancers-slider {
    position: relative;
}

.freelancers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    overflow: hidden;
}

.freelancer-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.freelancer-card:hover {
    transform: translateY(-5px);
}

.freelancer-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    object-fit: cover;
}

.freelancer-info h6 {
    color: #333;
    margin-bottom: 0.5rem;
}

.freelancer-info > p {
    color: #666;
    font-size: 14px;
    margin-bottom: 1rem;
}

.rating {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.stars i {
    color: #fbbf24;
    font-size: 14px;
}

.rating-text {
    color: #333;
    font-weight: 500;
    font-size: 14px;
}

.skills {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.skill {
    background-color: #e5e7eb;
    color: #374151;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.slider-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    z-index: 10;
}

.slider-btn:hover {
    background-color: #06B6D4;
    color: white;
    border-color: #06B6D4;
}

.prev-btn {
    left: -25px;
}

.next-btn {
    right: -25px;
}

/* Stats Section */
.stats-section {
    padding: 80px 0;
    background-color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.stats-section .stat-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    text-align: left;
}

.stat-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #06B6D4, #0891B2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.stat-content h5 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 2rem;
}

.stat-content p {
    color: #666;
    font-size: 14px;
}

.counter {
    color: #06B6D4;
}

/* Newsletter Section */
.newsletter-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #06B6D4 0%, #0891B2 100%);
    position: relative;
    overflow: hidden;
}

.newsletter-content {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 2rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.newsletter-images {
    position: relative;
}

.newsletter-bg,
.newsletter-shape {
    width: 100%;
    height: auto;
}

.newsletter-text {
    text-align: center;
    color: white;
}

.newsletter-text h4 {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.newsletter-text p {
    margin-bottom: 2rem;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    background: white;
    border-radius: 50px;
    padding: 8px;
    max-width: 400px;
    margin: 0 auto;
}

.newsletter-input {
    flex: 1;
    border: none;
    padding: 12px 20px;
    border-radius: 50px;
    outline: none;
    font-size: 16px;
}

.newsletter-btn {
    background-color: #1e293b;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 50px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.newsletter-btn:hover {
    background-color: #334155;
}

.newsletter-image img {
    width: 100%;
    height: auto;
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.faq-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: flex-start;
}

.faq-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.faq-item {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.faq-question {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: none;
    border: none;
    text-align: left;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    transition: background-color 0.3s ease;
}

.faq-question:hover {
    background-color: #f8f9fa;
}

.faq-number {
    background: linear-gradient(135deg, #06B6D4, #0891B2);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
}

.faq-question i {
    margin-left: auto;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 1.5rem 1.5rem;
    display: none;
}

.faq-item.active .faq-answer {
    display: block;
}

.faq-answer p {
    color: #666;
    line-height: 1.6;
}

.show-more-btn {
    background: none;
    border: 2px solid #06B6D4;
    color: #06B6D4;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.show-more-btn:hover {
    background-color: #06B6D4;
    color: white;
}

.faq-image img {
    width: 100%;
    border-radius: 12px;
}

/* Blog Section */
.blog-section {
    padding: 80px 0;
    background-color: white;
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.blog-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    cursor: pointer;
}

.blog-card:hover {
    transform: translateY(-5px);
}

.blog-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.blog-content {
    padding: 1.5rem;
}

.blog-content h6 {
    color: #333;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.blog-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 14px;
}

.blog-meta i {
    color: #06B6D4;
}

/* Footer */
.footer {
    background-color: #1f2937;
    color: white;
}

.footer-top {
    padding: 60px 0;
    background-color: #111827;
}

.footer-signup {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
}

.footer-signup-item {
    text-align: center;
}

.footer-signup-item h4 {
    margin-bottom: 1rem;
}

.footer-signup-item p {
    margin-bottom: 2rem;
    opacity: 0.8;
}

.footer-main {
    padding: 60px 0;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
}

.footer-column h5 {
    color: white;
    margin-bottom: 1.5rem;
}

.footer-column ul {
    list-style: none;
}

.footer-column ul li {
    margin-bottom: 0.5rem;
}

.footer-column ul li a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-column ul li a:hover {
    color: #06B6D4;
}

.contact-info li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.contact-info i {
    color: #06B6D4;
    width: 16px;
}

.social-links p {
    margin-bottom: 1rem;
    color: white;
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-icons a {
    width: 40px;
    height: 40px;
    background-color: #374151;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.social-icons a:hover {
    background-color: #06B6D4;
}

.footer-bottom {
    padding: 20px 0;
    border-top: 1px solid #374151;
    text-align: center;
}

.footer-bottom p {
    color: #d1d5db;
    margin: 0;
}

.footer-bottom a {
    color: #06B6D4;
    text-decoration: none;
}

/* Cookie Notice */
.cookie-notice {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #1f2937;
    color: white;
    padding: 20px;
    z-index: 1000;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.cookie-notice.show {
    transform: translateY(0);
}

.cookie-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cookie-icon {
    color: #4F46E5;
    font-size: 1.5rem;
}

.cookie-content p {
    flex: 1;
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
}

.cookie-link {
    color: #4F46E5;
    text-decoration: none;
}

.cookie-buttons {
    display: flex;
    gap: 0.5rem;
}

.cookie-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.allow-btn {
    background-color: #10B981;
    color: white;
}

.allow-btn:hover {
    background-color: #059669;
}

.reject-btn {
    background-color: #6B7280;
    color: white;
}

.reject-btn:hover {
    background-color: #4B5563;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-container,
    .find-task-content,
    .different-content,
    .completion-content,
    .faq-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .newsletter-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .steps-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .step-item:not(:last-child)::after {
        display: none;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .auth-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .search-container {
        flex-direction: column;
        gap: 1rem;
    }

    .search-filters {
        width: 100%;
    }

    .filter-select {
        flex: 1;
    }

    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .steps-grid {
        grid-template-columns: 1fr;
    }

    .signup-grid {
        grid-template-columns: 1fr;
    }

    .signup-card {
        flex-direction: column;
        text-align: center;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .freelancers-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .blog-grid {
        grid-template-columns: 1fr;
    }

    .footer-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-signup {
        grid-template-columns: 1fr;
    }

    .cookie-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .cookie-buttons {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-title {
        font-size: 2rem;
    }

    h2 {
        font-size: 2rem;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .footer-grid {
        grid-template-columns: 1fr;
    }

    .slider-btn {
        display: none;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }
